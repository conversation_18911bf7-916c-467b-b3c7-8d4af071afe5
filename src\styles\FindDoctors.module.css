.findDoctorsPage {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.searchSection {
  background: linear-gradient(135deg, #2aa7ff 0%, #0c7ce6 100%);
  padding: 40px 20px;
}

.resultsSection {
  padding: 40px 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.resultsHeader {
  margin-bottom: 32px;
}

.resultsCount {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.verificationNote {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.checkIcon {
  color: #28a745;
  font-weight: bold;
}

.resultsContent {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
}

.medicalCentersList {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.medicalCenterCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.cardContent {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 20px;
  align-items: start;
}

.hospitalIcon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconContainer {
  width: 60px;
  height: 60px;
  background: #e3f2fd;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.hospitalInfo {
  flex: 1;
}

.hospitalName {
  font-size: 20px;
  font-weight: 600;
  color: #2aa7ff;
  margin-bottom: 4px;
}

.hospitalType {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.hospitalLocation {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.ratingSection {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.ratingBadge {
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.thumbsUp {
  font-size: 10px;
}

.reviewCount {
  color: #666;
  font-size: 12px;
}

.priceSection {
  margin-bottom: 12px;
}

.price {
  color: #28a745;
  font-weight: 600;
  font-size: 16px;
  margin-right: 8px;
}

.originalPrice {
  color: #999;
  text-decoration: line-through;
  font-size: 14px;
  margin-right: 8px;
}

.consultation {
  color: #666;
  font-size: 12px;
}

.availability {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #28a745;
  font-size: 14px;
  font-weight: 500;
}

.availabilityIcon {
  font-size: 12px;
}

.bookingSection {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}

.timeSlots {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeSlot {
  padding: 8px 16px;
  border: 1px solid #2aa7ff;
  background: white;
  color: #2aa7ff;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeSlot:hover {
  background: #2aa7ff;
  color: white;
}

.viewAllSlots {
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #2aa7ff;
  color: #2aa7ff;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.viewAllSlots:hover {
  background: #2aa7ff;
  color: white;
}

.bookButton {
  padding: 12px 16px;
  background: #2aa7ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.bookButton:hover {
  background: #1e90ff;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.adBanner {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.adImage {
  width: 100%;
  height: auto;
  display: block;
}

.placeholderAd {
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
}

.placeholderAd h3 {
  margin-bottom: 8px;
  font-size: 18px;
}

.placeholderAd p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .resultsContent {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .cardContent {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .bookingSection {
    min-width: auto;
  }
  
  .timeSlots {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .sidebar {
    order: -1;
  }
}

@media (max-width: 480px) {
  .searchSection {
    padding: 20px 16px;
  }
  
  .resultsSection {
    padding: 20px 16px;
  }
  
  .medicalCenterCard {
    padding: 16px;
  }
  
  .hospitalName {
    font-size: 18px;
  }
  
  .resultsCount {
    font-size: 20px;
  }
}
