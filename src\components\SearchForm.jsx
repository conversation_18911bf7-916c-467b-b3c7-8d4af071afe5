import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../services/api';
import styles from '../styles/SearchForm.module.css';

const SearchForm = () => {
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [selectedState, setSelectedState] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [loadingStates, setLoadingStates] = useState(true);
  const [loadingCities, setLoadingCities] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // Fetch states on component mount
  useEffect(() => {
    const fetchStates = async () => {
      try {
        setLoadingStates(true);
        const statesData = await apiService.getStates();
        setStates(statesData);
        setError('');
      } catch (err) {
        setError('Failed to load states. Please try again.');
        console.error('Error fetching states:', err);
      } finally {
        setLoadingStates(false);
      }
    };

    fetchStates();
  }, []);

  // Fetch cities when state changes
  useEffect(() => {
    const fetchCities = async () => {
      if (!selectedState) {
        setCities([]);
        setSelectedCity('');
        return;
      }

      try {
        setLoadingCities(true);
        const citiesData = await apiService.getCities(selectedState);
        setCities(citiesData);
        setSelectedCity('');
        setError('');
      } catch (err) {
        setError(`Failed to load cities for ${selectedState}. Please try again.`);
        console.error('Error fetching cities:', err);
        setCities([]);
      } finally {
        setLoadingCities(false);
      }
    };

    fetchCities();
  }, [selectedState]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!selectedState || !selectedCity) {
      setError('Please select both state and city');
      return;
    }

    // Navigate to search results with state and city parameters
    navigate(`/search?state=${encodeURIComponent(selectedState)}&city=${encodeURIComponent(selectedCity)}`);
  };

  return (
    <div className={styles.searchContainer}>
      <div className={styles.searchCard}>
        <h2 className={styles.title}>Find Medical Centers</h2>
        <p className={styles.subtitle}>Search for medical centers in your area</p>
        
        {error && <div className={styles.error}>{error}</div>}
        
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="state" className={styles.label}>State</label>
              <div id="state">
                <select
                  value={selectedState}
                  onChange={(e) => setSelectedState(e.target.value)}
                  className={styles.select}
                  disabled={loadingStates}
                >
                  <option value="">Select State</option>
                  {states.map((state) => (
                    <option key={state} value={state}>
                      {state}
                    </option>
                  ))}
                </select>
              </div>
              {loadingStates && <div className={styles.loading}>Loading states...</div>}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="city" className={styles.label}>City</label>
              <div id="city">
                <select
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                  className={styles.select}
                  disabled={!selectedState || loadingCities}
                >
                  <option value="">Select City</option>
                  {cities.map((city) => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
              </div>
              {loadingCities && <div className={styles.loading}>Loading cities...</div>}
            </div>

            <button 
              type="submit" 
              id="searchBtn"
              className={styles.searchButton}
              disabled={!selectedState || !selectedCity || loadingStates || loadingCities}
            >
              Search
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SearchForm;
