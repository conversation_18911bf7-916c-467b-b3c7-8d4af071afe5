import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import styles from '../styles/SpecialistsCarousel.module.css';

const SpecialistsCarousel = () => {
  const specialists = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      specialty: 'Neurologist',
      image: '/api/placeholder/200/200',
      experience: '15+ years'
    },
    {
      id: 2,
      name: 'Dr. <PERSON><PERSON>',
      specialty: 'Orthopaedics',
      image: '/api/placeholder/200/200',
      experience: '12+ years'
    },
    {
      id: 3,
      name: 'Dr. <PERSON><PERSON><PERSON>',
      specialty: 'Medicine',
      image: '/api/placeholder/200/200',
      experience: '10+ years'
    },
    {
      id: 4,
      name: 'Dr. <PERSON>',
      specialty: 'Cardiology',
      image: '/api/placeholder/200/200',
      experience: '18+ years'
    },
    {
      id: 5,
      name: 'Dr. <PERSON>',
      specialty: 'Pediatrics',
      image: '/api/placeholder/200/200',
      experience: '14+ years'
    },
    {
      id: 6,
      name: 'Dr. <PERSON>',
      specialty: 'Dermatology',
      image: '/api/placeholder/200/200',
      experience: '11+ years'
    }
  ];

  return (
    <div className={styles.carouselContainer}>
      <h2 className={styles.sectionTitle}>Our Medical Specialist</h2>
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={30}
        slidesPerView={1}
        navigation
        pagination={{ clickable: true }}
        autoplay={{
          delay: 3000,
          disableOnInteraction: false,
        }}
        breakpoints={{
          640: {
            slidesPerView: 2,
          },
          768: {
            slidesPerView: 3,
          },
          1024: {
            slidesPerView: 4,
          },
        }}
        className={styles.swiper}
      >
        {specialists.map((specialist) => (
          <SwiperSlide key={specialist.id}>
            <div className={styles.specialistCard}>
              <img 
                src={specialist.image} 
                alt={specialist.name}
                className={styles.specialistImage}
              />
              <h3 className={styles.specialistName}>{specialist.name}</h3>
              <p className={styles.specialistSpecialty}>{specialist.specialty}</p>
              <p className={styles.specialistExperience}>{specialist.experience}</p>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default SpecialistsCarousel;
