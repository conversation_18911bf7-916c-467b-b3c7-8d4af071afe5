.searchResults {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.header {
  text-align: center;
  margin-bottom: 48px;
}

.resultsTitle {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.resultsSubtitle {
  font-size: 16px;
  color: #666;
}

.resultsGrid {
  display: grid;
  gap: 24px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2aa7ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  margin: 40px 0;
  border: 1px solid #f5c6cb;
}

.noResults {
  text-align: center;
  padding: 80px 20px;
}

.noResults h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
}

.noResults p {
  font-size: 16px;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 20px 16px;
  }
  
  .resultsTitle {
    font-size: 24px;
  }
  
  .header {
    margin-bottom: 32px;
  }
}
