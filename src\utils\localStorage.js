// LocalStorage utility functions for managing bookings
export const localStorageService = {
  // Get all bookings from localStorage
  getBookings() {
    try {
      const bookings = localStorage.getItem('bookings');
      return bookings ? JSON.parse(bookings) : [];
    } catch (error) {
      console.error('Error reading bookings from localStorage:', error);
      return [];
    }
  },

  // Save a new booking to localStorage
  saveBooking(booking) {
    try {
      const existingBookings = this.getBookings();
      const newBooking = {
        id: Date.now().toString(),
        ...booking,
        bookingDate: new Date().toISOString()
      };
      const updatedBookings = [...existingBookings, newBooking];
      localStorage.setItem('bookings', JSON.stringify(updatedBookings));
      return newBooking;
    } catch (error) {
      console.error('Error saving booking to localStorage:', error);
      throw error;
    }
  },

  // Remove a booking from localStorage
  removeBooking(bookingId) {
    try {
      const existingBookings = this.getBookings();
      const updatedBookings = existingBookings.filter(booking => booking.id !== bookingId);
      localStorage.setItem('bookings', JSON.stringify(updatedBookings));
      return true;
    } catch (error) {
      console.error('Error removing booking from localStorage:', error);
      return false;
    }
  },

  // Clear all bookings
  clearBookings() {
    try {
      localStorage.removeItem('bookings');
      return true;
    } catch (error) {
      console.error('Error clearing bookings from localStorage:', error);
      return false;
    }
  }
};
