// Test helper functions for validating application functionality
export const testHelpers = {
  // Test localStorage functionality
  testLocalStorage() {
    try {
      // Test saving a booking
      const testBooking = {
        hospitalName: 'Test Hospital',
        address: '123 Test St',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        appointmentDate: '2024-01-15',
        appointmentTime: '10:00 AM',
        timePeriod: 'Morning'
      };

      localStorage.setItem('bookings', JSON.stringify([testBooking]));
      const retrieved = JSON.parse(localStorage.getItem('bookings'));
      
      if (retrieved && retrieved.length === 1 && retrieved[0].hospitalName === 'Test Hospital') {
        console.log('✅ localStorage test passed');
        localStorage.removeItem('bookings'); // Clean up
        return true;
      } else {
        console.log('❌ localStorage test failed');
        return false;
      }
    } catch (error) {
      console.error('❌ localStorage test error:', error);
      return false;
    }
  },

  // Test API endpoints
  async testAPIEndpoints() {
    try {
      console.log('🔄 Testing API endpoints...');
      
      // Test states endpoint
      const statesResponse = await fetch('https://meddata-backend.onrender.com/states');
      const states = await statesResponse.json();
      
      if (!Array.isArray(states) || states.length === 0) {
        console.log('❌ States API test failed');
        return false;
      }
      console.log('✅ States API test passed');

      // Test cities endpoint
      const citiesResponse = await fetch('https://meddata-backend.onrender.com/cities/Alaska');
      const cities = await citiesResponse.json();
      
      if (!Array.isArray(cities) || cities.length === 0) {
        console.log('❌ Cities API test failed');
        return false;
      }
      console.log('✅ Cities API test passed');

      // Test medical centers endpoint
      const centersResponse = await fetch('https://meddata-backend.onrender.com/data?state=Alaska&city=SOLDOTNA');
      const centers = await centersResponse.json();
      
      if (!Array.isArray(centers) || centers.length === 0) {
        console.log('❌ Medical centers API test failed');
        return false;
      }
      console.log('✅ Medical centers API test passed');

      console.log('✅ All API tests passed');
      return true;
    } catch (error) {
      console.error('❌ API test error:', error);
      return false;
    }
  },

  // Test required HTML elements
  testRequiredElements() {
    const requiredElements = [
      { selector: '#state', name: 'State dropdown container' },
      { selector: '#city', name: 'City dropdown container' },
      { selector: '#searchBtn', name: 'Search button' }
    ];

    let allFound = true;
    
    requiredElements.forEach(({ selector, name }) => {
      const element = document.querySelector(selector);
      if (element) {
        console.log(`✅ ${name} found`);
      } else {
        console.log(`❌ ${name} not found`);
        allFound = false;
      }
    });

    return allFound;
  },

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting XMedify application tests...');
    
    const localStorageTest = this.testLocalStorage();
    const apiTest = await this.testAPIEndpoints();
    const elementsTest = this.testRequiredElements();
    
    const allPassed = localStorageTest && apiTest && elementsTest;
    
    if (allPassed) {
      console.log('🎉 All tests passed! XMedify application is ready.');
    } else {
      console.log('⚠️ Some tests failed. Please check the implementation.');
    }
    
    return allPassed;
  }
};

// Auto-run tests in development mode
if (import.meta.env.DEV) {
  window.testXMedify = testHelpers.runAllTests;
  console.log('💡 Run testXMedify() in console to test the application');
}
