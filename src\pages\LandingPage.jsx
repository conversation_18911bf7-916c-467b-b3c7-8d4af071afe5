import Navigation from "../components/Navigation";
import SearchForm from "../components/SearchForm";
import SpecialistsCarousel from "../components/SpecialistsCarousel";
import NewsCarousel from "../components/NewsCarousel";
import styles from "../styles/LandingPage.module.css";
import hero_image from "../assets/hero_image.png";

const LandingPage = () => {
  return (
    <div className={styles.landingPage}>
      <Navigation />

      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <h5 className={styles.heroTitle}>Skip the travel! Find Online</h5>
            <h1>
              <span className={styles.medical}>Medical </span>
              <span className={styles.centers}>Centers</span>
            </h1>
            <p className={styles.heroSubtitle}>
              Connect instantly with a 24x7 specialist or choose to video visit
              a particular doctor.
            </p>
            <button className={styles.ctaButton}>Find Centers</button>
          </div>
          <div className={styles.heroImage}>
            <img
              src={hero_image}
              alt="Medical professionals"
              className={styles.heroImg}
            />
          </div>
        </div>
      </section>

      {/* Search Section */}
      <SearchForm />

      {/* Features Section */}
      <section className={styles.featuresSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>You may be looking for</h2>
          <div className={styles.featuresGrid}>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🩺</div>
              <h3 className={styles.featureTitle}>Doctors</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🏥</div>
              <h3 className={styles.featureTitle}>Labs</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🏥</div>
              <h3 className={styles.featureTitle}>Hospitals</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>💊</div>
              <h3 className={styles.featureTitle}>Medical Store</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🚑</div>
              <h3 className={styles.featureTitle}>Ambulance</h3>
            </div>
          </div>
        </div>
      </section>

      {/* Specialists Section */}
      <SpecialistsCarousel />

      {/* News Section */}
      <NewsCarousel />

      {/* Patient Caring Section */}
      <section className={styles.patientCaringSection}>
        <div className={styles.container}>
          <div className={styles.patientCaringContent}>
            <div className={styles.patientCaringText}>
              <h2 className={styles.sectionTitle}>
                Helping patients from around the globe!!
              </h2>
              <div className={styles.statsGrid}>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>5000+</h3>
                  <p className={styles.statLabel}>Patients cared</p>
                </div>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>200+</h3>
                  <p className={styles.statLabel}>Hospitals</p>
                </div>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>1000+</h3>
                  <p className={styles.statLabel}>Laboratories</p>
                </div>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>700+</h3>
                  <p className={styles.statLabel}>Expert doctors</p>
                </div>
              </div>
            </div>
            <div className={styles.patientCaringImage}>
              <img
                src="/api/placeholder/400/300"
                alt="Patient caring"
                className={styles.patientImg}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className={styles.footer}>
        <div className={styles.container}>
          <p>&copy; 2024 Medify. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
