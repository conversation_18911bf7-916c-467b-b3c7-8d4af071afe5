import Navigation from "../components/Navigation";
import SearchForm from "../components/SearchForm";
import SpecialistsCarousel from "../components/SpecialistsCarousel";
import NewsCarousel from "../components/NewsCarousel";
import FAQ from "../components/FAQ";
import AppDownload from "../components/AppDownload";
import Footer from "../components/Footer";
import styles from "../styles/LandingPage.module.css";
import hero_image from "../assets/hero_image.png";
import care1 from "../assets/care1.png";

const LandingPage = () => {
  return (
    <div className={styles.landingPage}>
      <Navigation />

      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <h5 className={styles.heroTitle}>Skip the travel! Find Online</h5>
            <h1>
              <span className={styles.medical}>Medical </span>
              <span className={styles.centers}>Centers</span>
            </h1>
            <p className={styles.heroSubtitle}>
              Connect instantly with a 24x7 specialist or choose to video visit
              a particular doctor.
            </p>
            <button className={styles.ctaButton}>Find Centers</button>
          </div>
          <div className={styles.heroImage}>
            <img
              src={hero_image}
              alt="Medical professionals"
              className={styles.heroImg}
            />
          </div>
        </div>
      </section>

      {/* Search Section */}
      <SearchForm />

      {/* Features Section */}
      <section className={styles.featuresSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Find by specialisation</h2>
          <div className={styles.featuresGrid}>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🦷</div>
              <h3 className={styles.featureTitle}>Dentistry</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🩺</div>
              <h3 className={styles.featureTitle}>Primary Care</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>❤️</div>
              <h3 className={styles.featureTitle}>Cardiology</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🧲</div>
              <h3 className={styles.featureTitle}>MRI Resonance</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🩸</div>
              <h3 className={styles.featureTitle}>Blood Test</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🧠</div>
              <h3 className={styles.featureTitle}>Piscologist</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🔬</div>
              <h3 className={styles.featureTitle}>Laboratory</h3>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>📷</div>
              <h3 className={styles.featureTitle}>X-Ray</h3>
            </div>
          </div>
          <div className={styles.viewAllContainer}>
            <button className={styles.viewAllButton}>View All</button>
          </div>
        </div>
      </section>

      {/* Specialists Section */}
      <SpecialistsCarousel />

      {/* Patient Care Section */}
      <section className={styles.patientCareSection}>
        <div className={styles.container}>
          <div className={styles.patientCareContent}>
            <div className={styles.patientCareText}>
              <h2 className={styles.patientCareTitle}>
                Patient <span className={styles.highlight}>Caring</span>
              </h2>
              <p className={styles.patientCareSubtitle}>
                Our goal is to deliver quality of care in a courteous,
                respectful, and compassionate manner. We hope you will allow us
                to care for you and strive to be the first and best choice for
                healthcare.
              </p>
              <ul className={styles.patientCareList}>
                <li>✓ Stay Updated About Your Health</li>
                <li>✓ Check Your Results Online</li>
                <li>✓ Manage Your Appointments</li>
              </ul>
            </div>
            <div className={styles.patientCareImages}>
              <div className={styles.careImageContainer}>
                <img
                  src="/src/assets/care1.png"
                  alt="Patient Care 1"
                  className={styles.careImage}
                />
              </div>
              <div className={styles.careImageContainer}>
                <img
                  src="/src/assets/care2.png"
                  alt="Patient Care 2"
                  className={styles.careImage}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* News Section */}
      <NewsCarousel />

      {/* Patient Caring Section */}
      <section className={styles.patientCaringSection}>
        <div className={styles.container}>
          <div className={styles.patientCaringContent}>
            <div className={styles.patientCaringText}>
              <h2 className={styles.sectionTitle}>
                Helping patients from around the globe!!
              </h2>
              <div className={styles.statsGrid}>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>5000+</h3>
                  <p className={styles.statLabel}>Patients cared</p>
                </div>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>200+</h3>
                  <p className={styles.statLabel}>Hospitals</p>
                </div>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>1000+</h3>
                  <p className={styles.statLabel}>Laboratories</p>
                </div>
                <div className={styles.statItem}>
                  <h3 className={styles.statNumber}>700+</h3>
                  <p className={styles.statLabel}>Expert doctors</p>
                </div>
              </div>
            </div>
            <div className={styles.patientCaringImage}>
              <img
                src={care1}
                alt="Patient caring"
                className={styles.patientImg}
              />
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />

      {/* App Download Section */}
      <AppDownload />

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default LandingPage;
