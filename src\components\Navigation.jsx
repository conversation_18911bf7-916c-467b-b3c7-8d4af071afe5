import { Link, useLocation } from 'react-router-dom';
import styles from '../styles/Navigation.module.css';

const Navigation = () => {
  const location = useLocation();

  return (
    <nav className={styles.navbar}>
      <div className={styles.container}>
        <Link to="/" className={styles.logo}>
          <span className={styles.logoText}>Medify</span>
        </Link>
        
        <div className={styles.navLinks}>
          <Link 
            to="/" 
            className={`${styles.navLink} ${location.pathname === '/' ? styles.active : ''}`}
          >
            Find Doctors
          </Link>
          <span className={styles.navLink}>Hospitals</span>
          <span className={styles.navLink}>Medicines</span>
          <span className={styles.navLink}>Surgeries</span>
          <span className={styles.navLink}>Software for Provider</span>
          <span className={styles.navLink}>Facilities</span>
          <Link 
            to="/my-bookings" 
            className={`${styles.navLink} ${location.pathname === '/my-bookings' ? styles.active : ''}`}
          >
            My Bookings
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
