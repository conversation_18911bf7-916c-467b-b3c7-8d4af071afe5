.carouselContainer {
  padding: 80px 20px;
  background-color: #f8f9fa;
  max-width: 1200px;
  margin: 0 auto;
}

.sectionTitle {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 48px;
}

.swiper {
  padding-bottom: 50px;
}

.specialistCard {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.specialistCard:hover {
  transform: translateY(-8px);
}

.specialistImage {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 16px;
  border: 4px solid #e9ecef;
}

.specialistName {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.specialistSpecialty {
  font-size: 16px;
  color: #2aa7ff;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.specialistExperience {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Swiper navigation customization */
:global(.swiper-button-next),
:global(.swiper-button-prev) {
  color: #2aa7ff;
  background: white;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:global(.swiper-button-next:after),
:global(.swiper-button-prev:after) {
  font-size: 18px;
  font-weight: bold;
}

:global(.swiper-pagination-bullet) {
  background: #2aa7ff;
  opacity: 0.3;
}

:global(.swiper-pagination-bullet-active) {
  opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .carouselContainer {
    padding: 40px 16px;
  }
  
  .sectionTitle {
    font-size: 24px;
    margin-bottom: 32px;
  }
  
  .specialistCard {
    padding: 24px 16px;
  }
  
  .specialistImage {
    width: 100px;
    height: 100px;
  }
  
  .specialistName {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .specialistCard {
    padding: 20px 12px;
  }
  
  .specialistImage {
    width: 80px;
    height: 80px;
  }
  
  .specialistName {
    font-size: 16px;
  }
  
  .specialistSpecialty {
    font-size: 14px;
  }
}
