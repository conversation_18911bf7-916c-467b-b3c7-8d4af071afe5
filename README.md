# XMedify - Medical Center Booking Application

A React-based web application that allows users to find medical centers across the USA and book appointments.

## Features

### 🏥 Landing Page

- Top navigation bar with access to different sections
- Hero section with call-to-action
- Search functionality for states and cities
- Features showcase section
- Medical specialists carousel (using Swiper)
- News carousel
- Patient statistics section

### 🔍 Search Functionality

- State dropdown with all US states
- City dropdown populated based on selected state
- Real-time API integration for fetching data
- Search button with proper validation

### 📋 Search Results

- Display of available medical centers
- Hospital information including name, address, and ratings
- "Book FREE Center Visit" buttons
- Responsive grid layout

### 📅 Booking Interface

- Calendar view for selecting appointment dates (up to 7 days in advance)
- Time slot selection with Morning, Afternoon, and Evening periods
- Interactive booking modal
- Appointment confirmation

### 📖 My Bookings Page

- Personalized view of all user bookings
- Booking details including hospital info, date, and time
- Cancel appointment functionality
- Empty state for no bookings

### 💾 Data Persistence

- localStorage integration for booking data
- Persistent bookings across page refreshes
- Booking management (add/remove)

### 📱 Responsive Design

- Mobile-first approach
- CSS modules for component-scoped styling
- Responsive breakpoints for all screen sizes
- Touch-friendly interface

## Technology Stack

- **Frontend**: React 18 with Vite
- **Routing**: React Router DOM
- **Styling**: CSS Modules
- **Carousels**: Swiper.js
- **API Integration**: Fetch API
- **Data Storage**: localStorage

## API Endpoints

The application integrates with the following backend endpoints:

- **Get States**: `https://meddata-backend.onrender.com/states`
- **Get Cities**: `https://meddata-backend.onrender.com/cities/:state`
- **Get Medical Centers**: `https://meddata-backend.onrender.com/data?state=<state>&city=<city>`

## Installation & Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd XMedify
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start the development server**

   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173` (or the port shown in terminal)

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Navigation.jsx
│   ├── SearchForm.jsx
│   ├── MedicalCenterCard.jsx
│   ├── BookingModal.jsx
│   ├── BookingCard.jsx
│   ├── SpecialistsCarousel.jsx
│   └── NewsCarousel.jsx
├── pages/              # Page components
│   ├── LandingPage.jsx
│   ├── SearchResults.jsx
│   └── MyBookings.jsx
├── services/           # API service functions
│   └── api.js
├── utils/              # Utility functions
│   ├── localStorage.js
│   ├── dateUtils.js
│   └── testHelpers.js
├── styles/             # CSS modules
│   ├── App.css
│   ├── Navigation.module.css
│   ├── SearchForm.module.css
│   ├── LandingPage.module.css
│   ├── SearchResults.module.css
│   ├── MyBookings.module.css
│   ├── MedicalCenterCard.module.css
│   ├── BookingModal.module.css
│   ├── BookingCard.module.css
│   ├── SpecialistsCarousel.module.css
│   └── NewsCarousel.module.css
├── App.jsx             # Main app component with routing
└── main.jsx            # Application entry point
```

## Key Requirements Implemented

### HTML Elements & IDs

- ✅ State dropdown container: `<div id="state">`
- ✅ City dropdown container: `<div id="city">`
- ✅ Search button: `<button id="searchBtn">`
- ✅ My Bookings heading: `<h1>My Bookings</h1>`
- ✅ Hospital names: `<h3>` tags
- ✅ Search results heading: `<h1>{count} medical centers available in {city}</h1>`
- ✅ Book button: "Book FREE Center Visit"
- ✅ Time period display: `<p>Today</p>`, `<p>Morning</p>`, etc.

### Routing

- ✅ Landing page: `/`
- ✅ Search results: `/search`
- ✅ My bookings: `/my-bookings`

### Data Storage

- ✅ localStorage key: `'bookings'`
- ✅ Persistent booking data
- ✅ CRUD operations for bookings

## Testing

The application includes built-in test helpers. Open the browser console and run:

```javascript
testXMedify();
```

This will test:

- localStorage functionality
- API endpoint connectivity
- Required HTML elements presence

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Considerations

- Lazy loading for images
- Optimized API calls with error handling
- Efficient state management
- CSS modules for optimized styling
- Responsive images and layouts

---

**Note**: The backend API may take 50-60 seconds to respond on first request due to cold start delays.
