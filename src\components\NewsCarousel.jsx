import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import styles from '../styles/NewsCarousel.module.css';

const NewsCarousel = () => {
  const newsItems = [
    {
      id: 1,
      title: 'Medical Care is available 24/7',
      description: 'Get instant access to medical professionals anytime, anywhere.',
      image: '/api/placeholder/300/200',
      date: '2024-01-15'
    },
    {
      id: 2,
      title: 'Patient Caring: Our Latest News',
      description: 'Read about our commitment to providing the best patient care.',
      image: '/api/placeholder/300/200',
      date: '2024-01-10'
    },
    {
      id: 3,
      title: 'Download the Medify App',
      description: 'Get our mobile app for easier access to medical services.',
      image: '/api/placeholder/300/200',
      date: '2024-01-05'
    }
  ];

  return (
    <section className={styles.newsSection}>
      <div className={styles.container}>
        <h2 className={styles.sectionTitle}>Read Our Latest News</h2>
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={30}
          slidesPerView={1}
          navigation
          pagination={{ clickable: true }}
          autoplay={{
            delay: 4000,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 2,
            },
            1024: {
              slidesPerView: 3,
            },
          }}
          className={styles.swiper}
        >
          {newsItems.map((item) => (
            <SwiperSlide key={item.id}>
              <div className={styles.newsCard}>
                <img 
                  src={item.image} 
                  alt={item.title}
                  className={styles.newsImage}
                />
                <div className={styles.newsContent}>
                  <p className={styles.newsDate}>
                    {new Date(item.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                  <h3 className={styles.newsTitle}>{item.title}</h3>
                  <p className={styles.newsDescription}>{item.description}</p>
                  <button className={styles.readMoreButton}>Read More</button>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
};

export default NewsCarousel;
