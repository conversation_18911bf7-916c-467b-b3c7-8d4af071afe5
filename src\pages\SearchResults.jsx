import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Navigation from '../components/Navigation';
import MedicalCenterCard from '../components/MedicalCenterCard';
import BookingModal from '../components/BookingModal';
import { apiService } from '../services/api';
import styles from '../styles/SearchResults.module.css';

const SearchResults = () => {
  const [searchParams] = useSearchParams();
  const [medicalCenters, setMedicalCenters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCenter, setSelectedCenter] = useState(null);
  const [showBookingModal, setShowBookingModal] = useState(false);

  const state = searchParams.get('state');
  const city = searchParams.get('city');

  useEffect(() => {
    const fetchMedicalCenters = async () => {
      if (!state || !city) {
        setError('State and city parameters are required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError('');
        const centers = await apiService.getMedicalCenters(state, city);
        setMedicalCenters(centers);
      } catch (err) {
        setError('Failed to load medical centers. Please try again.');
        console.error('Error fetching medical centers:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchMedicalCenters();
  }, [state, city]);

  const handleBookAppointment = (center) => {
    setSelectedCenter(center);
    setShowBookingModal(true);
  };

  const handleCloseModal = () => {
    setShowBookingModal(false);
    setSelectedCenter(null);
  };

  if (loading) {
    return (
      <div className={styles.searchResults}>
        <Navigation />
        <div className={styles.container}>
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading medical centers...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.searchResults}>
        <Navigation />
        <div className={styles.container}>
          <div className={styles.error}>{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.searchResults}>
      <Navigation />
      
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.resultsTitle}>
            {medicalCenters.length} medical centers available in {city}
          </h1>
          <p className={styles.resultsSubtitle}>
            Book appointments with verified doctors
          </p>
        </div>

        {medicalCenters.length === 0 ? (
          <div className={styles.noResults}>
            <h2>No medical centers found</h2>
            <p>Try searching in a different city or state.</p>
          </div>
        ) : (
          <div className={styles.resultsGrid}>
            {medicalCenters.map((center, index) => (
              <MedicalCenterCard
                key={index}
                center={center}
                onBookAppointment={() => handleBookAppointment(center)}
              />
            ))}
          </div>
        )}
      </div>

      {showBookingModal && selectedCenter && (
        <BookingModal
          center={selectedCenter}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default SearchResults;
