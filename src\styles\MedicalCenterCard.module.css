.centerCard {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.centerCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.centerInfo {
  flex: 1;
  margin-right: 24px;
}

.centerHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.centerName {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
  margin-right: 16px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars {
  color: #ffc107;
  font-size: 16px;
}

.ratingNumber {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.centerDetails {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.addressSection {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.addressIcon {
  font-size: 16px;
  margin-top: 2px;
}

.address {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

.features {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.feature {
  font-size: 14px;
  color: #28a745;
  font-weight: 500;
}

.bookingSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.availability {
  font-size: 14px;
  color: #28a745;
  font-weight: 600;
  margin: 0;
}

.bookButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.bookButton:hover {
  background-color: #1e90ff;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .centerCard {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
  
  .centerInfo {
    margin-right: 0;
  }
  
  .centerHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .centerName {
    margin-right: 0;
  }
  
  .bookingSection {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-width: auto;
  }
  
  .bookButton {
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .centerCard {
    padding: 16px;
  }
  
  .centerName {
    font-size: 18px;
  }
  
  .features {
    flex-direction: column;
    gap: 8px;
  }
  
  .bookingSection {
    flex-direction: column;
    gap: 12px;
  }
  
  .bookButton {
    width: 100%;
  }
}
