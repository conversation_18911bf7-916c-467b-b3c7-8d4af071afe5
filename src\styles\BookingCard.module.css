.bookingCard {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #2aa7ff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bookingCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.bookingCard.past {
  border-left-color: #6c757d;
  opacity: 0.8;
}

.bookingCard.upcoming {
  border-left-color: #28a745;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.hospitalInfo {
  flex: 1;
}

.hospitalName {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.address {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

.status {
  margin-left: 16px;
}

.statusBadge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.statusBadge.upcoming {
  background-color: #d4edda;
  color: #155724;
}

.statusBadge.past {
  background-color: #f8f9fa;
  color: #6c757d;
}

.appointmentDetails {
  margin-bottom: 20px;
}

.dateTimeSection {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 16px;
}

.dateInfo,
.timeInfo,
.periodInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
}

.value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.bookingMeta {
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.bookingDate {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cardActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.cancelButton {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

.appointmentType {
  display: flex;
  align-items: center;
}

.freeConsultation {
  background-color: #d1ecf1;
  color: #0c5460;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .bookingCard {
    padding: 16px;
  }
  
  .cardHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .status {
    margin-left: 0;
    align-self: flex-start;
  }
  
  .hospitalName {
    font-size: 18px;
  }
  
  .dateTimeSection {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .dateInfo,
  .timeInfo,
  .periodInfo {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
  }
  
  .cardActions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .cancelButton {
    width: 100%;
  }
  
  .appointmentType {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .bookingCard {
    padding: 12px;
  }
  
  .hospitalName {
    font-size: 16px;
  }
  
  .value {
    font-size: 14px;
  }
}
