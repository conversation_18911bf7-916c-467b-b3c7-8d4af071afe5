.downloadSection {
  padding: 80px 20px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.downloadContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.phoneImages {
  display: flex;
  justify-content: center;
  position: relative;
}

.phoneImage {
  width: 300px;
  height: auto;
}

.downloadInfo {
  max-width: 500px;
}

.downloadTitle {
  font-size: 48px;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
  margin-bottom: 16px;
}

.appName {
  color: #2aa7ff;
}

.downloadSubtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.phoneNumberSection {
  margin-bottom: 32px;
}

.phoneInput {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.countryCode {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.phoneField {
  flex: 1;
  padding: 16px 20px;
  border: none;
  outline: none;
  font-size: 16px;
}

.phoneField::placeholder {
  color: #999;
}

.sendButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.sendButton:hover {
  background-color: #1e90ff;
}

.storeButtons {
  display: flex;
  gap: 16px;
}

.storeButton {
  display: block;
  transition: transform 0.3s ease;
}

.storeButton:hover {
  transform: translateY(-2px);
}

.storeImage {
  height: 50px;
  width: auto;
  border-radius: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .downloadSection {
    padding: 40px 16px;
  }
  
  .downloadContent {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .downloadTitle {
    font-size: 32px;
  }
  
  .phoneImages {
    order: 2;
  }
  
  .downloadInfo {
    order: 1;
    max-width: none;
  }
  
  .phoneInput {
    flex-direction: column;
  }
  
  .countryCode {
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .storeButtons {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .downloadTitle {
    font-size: 24px;
  }
  
  .phoneImage {
    width: 140px;
  }
  
  .phoneImages {
    gap: 12px;
  }
  
  .storeButtons {
    flex-direction: column;
    align-items: center;
  }
}
