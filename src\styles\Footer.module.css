.footer {
  background-color: #1b3c74;
  color: white;
  padding: 60px 20px 20px 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.footerContent {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footerSection {
  display: flex;
  flex-direction: column;
}

.logo {
  margin-bottom: 24px;
}

.logoText {
  font-size: 28px;
  font-weight: bold;
  color: #2aa7ff;
}

.socialLinks {
  display: flex;
  gap: 12px;
}

.socialLink {
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: transform 0.3s ease;
}

.socialLink:hover {
  transform: translateY(-2px);
}

.socialLink span {
  font-size: 18px;
}

.footerLinks {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerLinks li {
  margin-bottom: 12px;
}

.footerLinks a {
  color: #b8c5d6;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footerLinks a:hover {
  color: white;
}

.footerBottom {
  border-top: 1px solid #2d4a73;
  padding-top: 20px;
  text-align: center;
}

.copyright {
  font-size: 14px;
  color: #b8c5d6;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .footer {
    padding: 40px 16px 16px 16px;
  }
  
  .footerContent {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 32px;
  }
  
  .footerSection:first-child {
    grid-column: 1 / -1;
    text-align: center;
  }
  
  .socialLinks {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footerContent {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }
  
  .footerSection:first-child {
    grid-column: auto;
  }
  
  .logoText {
    font-size: 24px;
  }
  
  .socialLink {
    width: 36px;
    height: 36px;
  }
  
  .socialLink span {
    font-size: 16px;
  }
}
