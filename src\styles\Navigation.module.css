.headerText{
  font-size: 16px;
  color: #ffffff;
  margin: 0 auto;
  text-align: center;
  padding: 16px 0;
  background-color: rgb(39, 117, 225);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
}
.navbar {
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 16px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logoText {
  font-size: 28px;
  font-weight: bold;
  color: #2aa7ff;
}

.navLinks {
  display: flex;
  align-items: center;
  gap: 32px;
}

.navLink {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.navLink:hover {
  color: #2aa7ff;
  background-color: #f8f9fa;
}

.navLink.active {
  color: #2aa7ff;
  background-color: #e3f2fd;
  font-weight: 600;
}

.bookingsButton {
  background-color: #2aa7ff;
  color: white;
  text-decoration: none;
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
}

.bookingsButton:hover {
  background-color: #1e90ff;
  transform: translateY(-1px);
}

.bookingsButton.active {
  background-color: #1e90ff;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .navLinks {
    gap: 16px;
    flex-wrap: wrap;
  }
  
  .navLink {
    font-size: 14px;
    padding: 6px 12px;
  }
  
  .logoText {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .navLinks {
    display: none;
  }
}
