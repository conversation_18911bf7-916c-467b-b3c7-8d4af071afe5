.landingPage {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.heroSection {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 80px 20px;
}

.heroContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.heroText {
  max-width: 600px;
}

.heroTitle {
  font-size: 36px;
  color: #333;
  line-height: 1.2;
  margin-bottom: 20px;
}

.medical {
  color: #333;
  font-size: 48px;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 20px;
}

.centers {
  color: #2aa7ff;
  font-size: 48px;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 20px;
}

.heroSubtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 32px;
  line-height: 1.6;
}

.ctaButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ctaButton:hover {
  background-color: #1e90ff;
  transform: translateY(-2px);
}

.heroImage {
  display: flex;
  justify-content: center;
  z-index: 1;
  height: '640px';
  width: '735px';
}

.heroImg {
  max-width: 100%;
  height: auto;
  border-radius: 16px;
}

/* Features Section */
.featuresSection {
  padding: 80px 20px;
  background-color: white;
}

.sectionTitle {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 48px;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.featureCard {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-8px);
}

.featureIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.featureTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Specialists Section */
.specialistsSection {
  padding: 80px 20px;
  background-color: #f8f9fa;
}

.specialistsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  max-width: 900px;
  margin: 0 auto;
}

.specialistCard {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.specialistCard:hover {
  transform: translateY(-8px);
}

.specialistImage {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 16px;
}

.specialistName {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.specialistTitle {
  font-size: 16px;
  color: #2aa7ff;
  font-weight: 500;
}

/* Patient Caring Section */
.patientCaringSection {
  padding: 80px 20px;
  background-color: white;
}

.patientCaringContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.statsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-top: 32px;
}

.statItem {
  text-align: center;
}

.statNumber {
  font-size: 36px;
  font-weight: bold;
  color: #2aa7ff;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 16px;
  color: #666;
}

.patientCaringImage {
  display: flex;
  justify-content: center;
}

.patientImg {
  max-width: 100%;
  height: auto;
  border-radius: 16px;
}

/* Footer */
.footer {
  background-color: #333;
  color: white;
  padding: 32px 20px;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .heroContent {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .heroTitle {
    font-size: 30px;
  }
  
  .patientCaringContent {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .statsGrid {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }
  
  .sectionTitle {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 22px;
  }
  
  .heroSubtitle {
    font-size: 16px;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .specialistsGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
