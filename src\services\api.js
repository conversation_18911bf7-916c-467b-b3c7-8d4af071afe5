const BASE_URL = 'https://meddata-backend.onrender.com';

// API service functions for medical center data
export const apiService = {
  // Fetch all states
  async getStates() {
    try {
      const response = await fetch(`${BASE_URL}/states`);
      if (!response.ok) {
        throw new Error('Failed to fetch states');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching states:', error);
      throw error;
    }
  },

  // Fetch cities for a specific state
  async getCities(state) {
    try {
      const response = await fetch(`${BASE_URL}/cities/${encodeURIComponent(state)}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch cities for ${state}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching cities for ${state}:`, error);
      throw error;
    }
  },

  // Fetch medical centers for a specific state and city
  async getMedicalCenters(state, city) {
    try {
      const response = await fetch(
        `${BASE_URL}/data?state=${encodeURIComponent(state)}&city=${encodeURIComponent(city)}`
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch medical centers for ${city}, ${state}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching medical centers for ${city}, ${state}:`, error);
      throw error;
    }
  }
};
