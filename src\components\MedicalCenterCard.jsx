import styles from '../styles/MedicalCenterCard.module.css';

const MedicalCenterCard = ({ center, onBookAppointment }) => {
  // Extract relevant information from the center object
  const hospitalName = center['Hospital Name'] || center.name || 'Unknown Hospital';
  const address = center['Address'] || center.address || '';
  const city = center['City'] || center.city || '';
  const state = center['State'] || center.state || '';
  const zipCode = center['ZIP Code'] || center.zipCode || '';
  const rating = center['Overall Rating'] || center.rating || 'N/A';

  // Format the full address
  const fullAddress = [address, city, state, zipCode].filter(Boolean).join(', ');

  // Generate star rating
  const renderStars = (rating) => {
    const numRating = parseFloat(rating);
    if (isNaN(numRating)) return 'No rating';
    
    const stars = [];
    const fullStars = Math.floor(numRating);
    const hasHalfStar = numRating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('★');
    }
    
    if (hasHalfStar) {
      stars.push('☆');
    }
    
    return (
      <div className={styles.rating}>
        <span className={styles.stars}>{stars.join('')}</span>
        <span className={styles.ratingNumber}>({numRating})</span>
      </div>
    );
  };

  return (
    <div className={styles.centerCard}>
      <div className={styles.centerInfo}>
        <div className={styles.centerHeader}>
          <h3 className={styles.centerName}>{hospitalName}</h3>
          {renderStars(rating)}
        </div>
        
        <div className={styles.centerDetails}>
          <div className={styles.addressSection}>
            <span className={styles.addressIcon}>📍</span>
            <p className={styles.address}>{fullAddress || 'Address not available'}</p>
          </div>
          
          <div className={styles.features}>
            <span className={styles.feature}>✓ Free consultation</span>
            <span className={styles.feature}>✓ Available today</span>
          </div>
        </div>
      </div>
      
      <div className={styles.bookingSection}>
        <p className={styles.availability}>Available Today</p>
        <button 
          className={styles.bookButton}
          onClick={onBookAppointment}
        >
          Book FREE Center Visit
        </button>
      </div>
    </div>
  );
};

export default MedicalCenterCard;
