import { useState } from 'react';
import styles from '../styles/FAQ.module.css';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const faqData = [
    {
      question: "Why choose our medical for your family?",
      answer: "We provide comprehensive healthcare services with experienced doctors, state-of-the-art facilities, and personalized care for every family member."
    },
    {
      question: "Why we are different from others?",
      answer: "Our unique approach combines advanced medical technology with compassionate care, ensuring the best possible outcomes for our patients."
    },
    {
      question: "Trusted & experience senior care & love",
      answer: "With years of experience in healthcare, our senior medical professionals provide trusted care with genuine compassion and expertise."
    },
    {
      question: "How to get appointment for emergency cases?",
      answer: "For emergency cases, you can call our 24/7 helpline or visit our emergency department directly. We also offer online emergency consultation booking."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className={styles.faqSection}>
      <div className={styles.container}>
        <div className={styles.faqContent}>
          <div className={styles.faqImage}>
            <img 
              src="/api/placeholder/400/300" 
              alt="Medical consultation" 
              className={styles.consultationImage}
            />
            <div className={styles.imageOverlay}>
              <div className={styles.overlayContent}>
                <span className={styles.overlayIcon}>💬</span>
                <p className={styles.overlayText}>84k+ Happy Patients</p>
              </div>
            </div>
          </div>
          
          <div className={styles.faqList}>
            <h2 className={styles.faqTitle}>Frequently Asked Questions</h2>
            
            <div className={styles.faqItems}>
              {faqData.map((item, index) => (
                <div key={index} className={styles.faqItem}>
                  <button 
                    className={`${styles.faqQuestion} ${openIndex === index ? styles.active : ''}`}
                    onClick={() => toggleFAQ(index)}
                  >
                    <span>{item.question}</span>
                    <span className={styles.faqIcon}>
                      {openIndex === index ? '−' : '+'}
                    </span>
                  </button>
                  
                  {openIndex === index && (
                    <div className={styles.faqAnswer}>
                      <p>{item.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
