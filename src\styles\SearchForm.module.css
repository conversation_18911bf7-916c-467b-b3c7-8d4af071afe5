.searchContainer {
  position: relative;
  bottom:250px;
  padding: 10px 20px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.searchCard {
  background: white;
  border-radius: 15px;
  padding: 64px 40px;
  box-shadow: 6px 6px 35px 0px rgba(24, 139, 255, 0.10);
  max-width: 1000px;
  width: 100%;
}

.form {
  width: 100%;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 20px;
  align-items: end;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.select {
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.select:focus {
  outline: none;
  border-color: #2aa7ff;
}

.select:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.searchButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  height: fit-content;
}

.searchButton:hover:not(:disabled) {
  background-color: #1e90ff;
  transform: translateY(-2px);
}

.searchButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.loading {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
  text-align: center;
}
/* You may be looking for Section */
.lookingForSection {
  padding: 70px 20px;
  background-color: #fff;
}

.lookingForGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.lookingForCard {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.lookingForCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.lookingForCard .lookingForIcon {
  font-size: 40px;
  margin-bottom: 16px;
}
.sectionTitle{
  font-size: 30px;
    color: #333;
    text-align: center;
    margin-bottom: 8px;
}

.lookingForTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .searchContainer {
    padding: 40px 16px;
  }
  
  .searchCard {
    padding: 24px;
  }
  .formRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .searchButton {
    width: 100%;
    padding: 16px;
  }
    .lookingForGrid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }
  
    .lookingForCard {
      padding: 24px 16px;
    }
}

@media (max-width: 480px) {
  .searchCard {
    padding: 20px;
  }
        .lookingForGrid {
          grid-template-columns: 1fr;
          gap: 16px;
        }
}
