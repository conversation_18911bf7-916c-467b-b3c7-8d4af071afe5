.searchContainer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 20px;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchCard {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
}

.title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 32px;
}

.form {
  width: 100%;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 20px;
  align-items: end;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.select {
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.select:focus {
  outline: none;
  border-color: #2aa7ff;
}

.select:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.searchButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  height: fit-content;
}

.searchButton:hover:not(:disabled) {
  background-color: #1e90ff;
  transform: translateY(-2px);
}

.searchButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.loading {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .searchContainer {
    padding: 40px 16px;
  }
  
  .searchCard {
    padding: 24px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .searchButton {
    width: 100%;
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .searchCard {
    padding: 20px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .subtitle {
    font-size: 14px;
  }
}
