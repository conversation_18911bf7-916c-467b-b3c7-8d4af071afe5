.adSection {
  padding: 40px 20px;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.adSwiper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.adSlide {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.adImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Swiper navigation buttons */
.adSwiper :global(.swiper-button-next),
.adSwiper :global(.swiper-button-prev) {
  color: #2aa7ff;
  background: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.adSwiper :global(.swiper-button-next:after),
.adSwiper :global(.swiper-button-prev:after) {
  font-size: 16px;
  font-weight: bold;
}

/* Swiper pagination */
.adSwiper :global(.swiper-pagination-bullet) {
  background: #2aa7ff;
  opacity: 0.5;
}

.adSwiper :global(.swiper-pagination-bullet-active) {
  opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .adSection {
    padding: 20px 16px;
  }
  
  .adSlide {
    height: 150px;
  }
  
  .adSwiper :global(.swiper-button-next),
  .adSwiper :global(.swiper-button-prev) {
    width: 32px;
    height: 32px;
    margin-top: -16px;
  }
  
  .adSwiper :global(.swiper-button-next:after),
  .adSwiper :global(.swiper-button-prev:after) {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .adSlide {
    height: 120px;
  }
}
