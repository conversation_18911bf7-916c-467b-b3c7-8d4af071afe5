import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import styles from '../styles/AdCarousel.module.css';

const AdCarousel = () => {
  const ads = [
    {
      id: 1,
      image: '/src/assets/ad1.png',
      alt: 'Medical Advertisement 1'
    },
    {
      id: 2,
      image: '/src/assets/ad2.png',
      alt: 'Medical Advertisement 2'
    }
  ];

  return (
    <section className={styles.adSection}>
      <div className={styles.container}>
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={20}
          slidesPerView={1}
          navigation
          pagination={{ clickable: true }}
          autoplay={{
            delay: 4000,
            disableOnInteraction: false,
          }}
          loop={true}
          className={styles.adSwiper}
        >
          {ads.map((ad) => (
            <SwiperSlide key={ad.id}>
              <div className={styles.adSlide}>
                <img 
                  src={ad.image} 
                  alt={ad.alt}
                  className={styles.adImage}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
};

export default AdCarousel;
