import { useState } from 'react';
import { dateUtils } from '../utils/dateUtils';
import { localStorageService } from '../utils/localStorage';
import styles from '../styles/BookingModal.module.css';

const BookingModal = ({ center, onClose }) => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('morning');
  const [isBooking, setIsBooking] = useState(false);
  const [bookingSuccess, setBookingSuccess] = useState(false);

  const availableDays = dateUtils.getNext7Days();
  const timeSlots = dateUtils.getTimeSlots();

  const handleDateSelect = (day) => {
    setSelectedDate(day);
    setSelectedTime('');
  };

  const handleTimeSelect = (time) => {
    setSelectedTime(time);
  };

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime) {
      alert('Please select both date and time');
      return;
    }

    setIsBooking(true);

    try {
      const booking = {
        hospitalName: center['Hospital Name'] || center.name || 'Unknown Hospital',
        address: center['Address'] || center.address || '',
        city: center['City'] || center.city || '',
        state: center['State'] || center.state || '',
        zipCode: center['ZIP Code'] || center.zipCode || '',
        appointmentDate: selectedDate.dateString,
        appointmentTime: selectedTime,
        timePeriod: dateUtils.getTimePeriod(selectedTime)
      };

      localStorageService.saveBooking(booking);
      setBookingSuccess(true);
      
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Error booking appointment:', error);
      alert('Failed to book appointment. Please try again.');
    } finally {
      setIsBooking(false);
    }
  };

  if (bookingSuccess) {
    return (
      <div className={styles.modalOverlay} onClick={onClose}>
        <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
          <div className={styles.successMessage}>
            <div className={styles.successIcon}>✓</div>
            <h2>Booking Confirmed!</h2>
            <p>Your appointment has been successfully booked.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>Book Appointment</h2>
          <button className={styles.closeButton} onClick={onClose}>×</button>
        </div>

        <div className={styles.modalContent}>
          <div className={styles.centerInfo}>
            <h3>{center['Hospital Name'] || center.name || 'Unknown Hospital'}</h3>
            <p>{[center['Address'], center['City'], center['State']].filter(Boolean).join(', ')}</p>
          </div>

          <div className={styles.bookingSection}>
            <div className={styles.dateSelection}>
              <h4>Select Date</h4>
              <div className={styles.dateGrid}>
                {availableDays.map((day, index) => (
                  <button
                    key={index}
                    className={`${styles.dateButton} ${
                      selectedDate?.dateString === day.dateString ? styles.selected : ''
                    }`}
                    onClick={() => handleDateSelect(day)}
                  >
                    <div className={styles.dayName}>
                      {day.isToday ? <p>Today</p> : day.dayName}
                    </div>
                    <div className={styles.dayNumber}>{day.dayNumber}</div>
                    <div className={styles.monthName}>{day.monthName}</div>
                  </button>
                ))}
              </div>
            </div>

            {selectedDate && (
              <div className={styles.timeSelection}>
                <h4>Select Time</h4>
                
                <div className={styles.timePeriods}>
                  <button
                    className={`${styles.periodButton} ${
                      selectedPeriod === 'morning' ? styles.activePeriod : ''
                    }`}
                    onClick={() => setSelectedPeriod('morning')}
                  >
                    <p>Morning</p>
                  </button>
                  <button
                    className={`${styles.periodButton} ${
                      selectedPeriod === 'afternoon' ? styles.activePeriod : ''
                    }`}
                    onClick={() => setSelectedPeriod('afternoon')}
                  >
                    <p>Afternoon</p>
                  </button>
                  <button
                    className={`${styles.periodButton} ${
                      selectedPeriod === 'evening' ? styles.activePeriod : ''
                    }`}
                    onClick={() => setSelectedPeriod('evening')}
                  >
                    <p>Evening</p>
                  </button>
                </div>

                <div className={styles.timeSlots}>
                  {timeSlots[selectedPeriod].map((time, index) => (
                    <button
                      key={index}
                      className={`${styles.timeButton} ${
                        selectedTime === time ? styles.selected : ''
                      }`}
                      onClick={() => handleTimeSelect(time)}
                    >
                      {time}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className={styles.modalActions}>
            <button className={styles.cancelButton} onClick={onClose}>
              Cancel
            </button>
            <button
              className={styles.bookButton}
              onClick={handleBooking}
              disabled={!selectedDate || !selectedTime || isBooking}
            >
              {isBooking ? 'Booking...' : 'Book Appointment'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
