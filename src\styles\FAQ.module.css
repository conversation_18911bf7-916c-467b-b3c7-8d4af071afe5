.faqSection {
  padding: 80px 20px;
  background-color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.faqContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.faqImage {
  position: relative;
}

.consultationImage {
  width: 100%;
  height: auto;
  border-radius: 16px;
  object-fit: cover;
}

.imageOverlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 12px;
}

.overlayIcon {
  font-size: 24px;
}

.overlayText {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.faqList {
  max-width: 500px;
}

.faqTitle {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 32px;
  line-height: 1.3;
}

.faqItems {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.faqItem {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.faqQuestion {
  width: 100%;
  padding: 20px 24px;
  background: none;
  border: none;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.faqQuestion:hover {
  background-color: #f8f9fa;
}

.faqQuestion.active {
  background-color: #e3f2fd;
  color: #2aa7ff;
}

.faqIcon {
  font-size: 20px;
  font-weight: bold;
  color: #2aa7ff;
  transition: transform 0.3s ease;
}

.faqQuestion.active .faqIcon {
  transform: rotate(180deg);
}

.faqAnswer {
  padding: 0 24px 20px 24px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  animation: slideDown 0.3s ease;
}

.faqAnswer p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 16px 0 0 0;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .faqSection {
    padding: 40px 16px;
  }
  
  .faqContent {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .faqTitle {
    font-size: 24px;
    margin-bottom: 24px;
  }
  
  .faqQuestion {
    padding: 16px 20px;
    font-size: 14px;
  }
  
  .faqAnswer {
    padding: 0 20px 16px 20px;
  }
  
  .imageOverlay {
    bottom: 16px;
    left: 16px;
    padding: 12px 16px;
  }
  
  .overlayText {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .faqQuestion {
    padding: 12px 16px;
  }
  
  .faqAnswer {
    padding: 0 16px 12px 16px;
  }
  
  .faqTitle {
    font-size: 20px;
  }
}
