import styles from '../styles/AppDownload.module.css';

const AppDownload = () => {
  return (
    <section className={styles.downloadSection}>
      <div className={styles.container}>
        <div className={styles.downloadContent}>
          <div className={styles.phoneImages}>
            <div className={styles.phoneContainer}>
              <img 
                src="/api/placeholder/200/400" 
                alt="Medify App Screenshot 1" 
                className={styles.phoneImage}
              />
            </div>
            <div className={styles.phoneContainer}>
              <img 
                src="/api/placeholder/200/400" 
                alt="Medify App Screenshot 2" 
                className={styles.phoneImage}
              />
            </div>
          </div>
          
          <div className={styles.downloadInfo}>
            <h2 className={styles.downloadTitle}>
              Download the
              <br />
              <span className={styles.appName}>Medify</span> App
            </h2>
            
            <p className={styles.downloadSubtitle}>
              Get the link to download the app
            </p>
            
            <div className={styles.phoneNumberSection}>
              <div className={styles.phoneInput}>
                <span className={styles.countryCode}>+91</span>
                <input 
                  type="tel" 
                  placeholder="Enter phone number"
                  className={styles.phoneField}
                />
                <button className={styles.sendButton}>Send SMS</button>
              </div>
            </div>
            
            <div className={styles.storeButtons}>
              <a href="#" className={styles.storeButton}>
                <img 
                  src="/api/placeholder/150/50" 
                  alt="Download on Google Play" 
                  className={styles.storeImage}
                />
              </a>
              <a href="#" className={styles.storeButton}>
                <img 
                  src="/api/placeholder/150/50" 
                  alt="Download on App Store" 
                  className={styles.storeImage}
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppDownload;
