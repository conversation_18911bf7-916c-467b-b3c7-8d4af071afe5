import styles from "../styles/AppDownload.module.css";

const AppDownload = () => {
  return (
    <section className={styles.downloadSection}>
      <div className={styles.container}>
        <div className={styles.downloadContent}>
          <div className={styles.phoneImages}>
            <img
              src="/src/assets/phones.png"
              alt="Medify App Screenshots"
              className={styles.phoneImage}
            />
          </div>

          <div className={styles.downloadInfo}>
            <h2 className={styles.downloadTitle}>
              Download the
              <br />
              <span className={styles.appName}>Medify</span> App
            </h2>

            <p className={styles.downloadSubtitle}>
              Get the link to download the app
            </p>

            <div className={styles.phoneNumberSection}>
              <div className={styles.phoneInput}>
                <span className={styles.countryCode}>+91</span>
                <input
                  type="tel"
                  placeholder="Enter phone number"
                  className={styles.phoneField}
                />
                <button className={styles.sendButton}>Send SMS</button>
              </div>
            </div>

            <div className={styles.storeButtons}>
              <a href="#" className={styles.storeButton}>
                <img
                  src="/src/assets/google_play.png.png"
                  alt="Download on Google Play"
                  className={styles.storeImage}
                />
              </a>
              <a href="#" className={styles.storeButton}>
                <img
                  src="/src/assets/apple_store.png.png"
                  alt="Download on App Store"
                  className={styles.storeImage}
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppDownload;
