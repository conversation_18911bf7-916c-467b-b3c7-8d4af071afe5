.newsSection {
  padding: 80px 20px;
  background-color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.sectionTitle {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 48px;
}

.swiper {
  padding-bottom: 50px;
}

.newsCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.newsCard:hover {
  transform: translateY(-8px);
}

.newsImage {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.newsContent {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.newsDate {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
}

.newsTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.newsDescription {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0 0 20px 0;
  flex: 1;
}

.readMoreButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.readMoreButton:hover {
  background-color: #1e90ff;
  transform: translateY(-1px);
}

/* Swiper navigation customization */
:global(.swiper-button-next),
:global(.swiper-button-prev) {
  color: #2aa7ff;
  background: white;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:global(.swiper-button-next:after),
:global(.swiper-button-prev:after) {
  font-size: 18px;
  font-weight: bold;
}

:global(.swiper-pagination-bullet) {
  background: #2aa7ff;
  opacity: 0.3;
}

:global(.swiper-pagination-bullet-active) {
  opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .newsSection {
    padding: 40px 16px;
  }
  
  .sectionTitle {
    font-size: 24px;
    margin-bottom: 32px;
  }
  
  .newsContent {
    padding: 16px;
  }
  
  .newsTitle {
    font-size: 16px;
  }
  
  .newsImage {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .newsContent {
    padding: 12px;
  }
  
  .newsTitle {
    font-size: 14px;
  }
  
  .newsDescription {
    font-size: 12px;
  }
  
  .readMoreButton {
    padding: 8px 16px;
    font-size: 12px;
  }
}
