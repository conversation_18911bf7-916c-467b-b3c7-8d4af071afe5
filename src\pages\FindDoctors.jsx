import Navigation from "../components/Navigation";
import SearchForm from "../components/SearchForm";
import FAQ from "../components/FAQ";
import Footer from "../components/Footer";
import styles from "../styles/FindDoctors.module.css";
import sensodyne_dweb from "../assets/sensodyne_dweb.png.png";

const FindDoctors = () => {
  // Sample medical centers data
  const medicalCenters = [
    {
      id: 1,
      name: "Fortis Hospital Richmond Road",
      type: "Multi Speciality",
      location: "Richmond Road, Bangalore",
      rating: 4.3,
      reviews: 1234,
      price: "FREE",
      originalPrice: "₹500",
      availability: "Available Today",
      timeSlots: [
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "01:30 PM",
        "02:00 PM",
        "02:30 PM",
      ],
    },
    {
      id: 2,
      name: "Fortis Hospital Bannerghatta Road",
      type: "Multi Speciality",
      location: "Bannerghatta Road, Bangalore",
      rating: 4.5,
      reviews: 2156,
      price: "FREE",
      originalPrice: "₹500",
      availability: "Available Today",
      timeSlots: [
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "01:30 PM",
        "02:00 PM",
        "02:30 PM",
      ],
    },
    {
      id: 3,
      name: "Fortis Hospital Nagarbhavi",
      type: "Multi Speciality",
      location: "Nagarbhavi, Bangalore",
      rating: 4.2,
      reviews: 987,
      price: "FREE",
      originalPrice: "₹500",
      availability: "Available Today",
      timeSlots: [
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "01:30 PM",
        "02:00 PM",
        "02:30 PM",
      ],
    },
    {
      id: 4,
      name: "Fortis Hospital Rajajinagar",
      type: "Multi Speciality",
      location: "Rajajinagar, Bangalore",
      rating: 4.4,
      reviews: 1567,
      price: "FREE",
      originalPrice: "₹500",
      availability: "Available Today",
      timeSlots: [
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "01:30 PM",
        "02:00 PM",
        "02:30 PM",
      ],
    },
    {
      id: 5,
      name: "Fortis Hospital Cunningham Road",
      type: "Multi Speciality",
      location: "Cunningham Road, Bangalore",
      rating: 4.1,
      reviews: 876,
      price: "FREE",
      originalPrice: "₹500",
      availability: "Available Today",
      timeSlots: [
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "01:30 PM",
        "02:00 PM",
        "02:30 PM",
      ],
    },
  ];

  return (
    <div className={styles.findDoctorsPage}>
      {/* Navigation */}
      <Navigation />

      {/* Search Section */}
      <section className={styles.searchSection}>
        <SearchForm />
      </section>

      {/* Results Section */}
      <section className={styles.resultsSection}>
        <div className={styles.container}>
          <div className={styles.resultsHeader}>
            <h2 className={styles.resultsCount}>
              {medicalCenters.length} medical centers available in Alaska
            </h2>
            <div className={styles.verificationNote}>
              <span className={styles.checkIcon}>✓</span>
              Book appointments with minimum wait-time & verified doctor details
            </div>
          </div>

          <div className={styles.resultsContent}>
            <div className={styles.medicalCentersList}>
              {medicalCenters.map((center) => (
                <div key={center.id} className={styles.medicalCenterCard}>
                  <div className={styles.cardContent}>
                    <div className={styles.hospitalIcon}>
                      <div className={styles.iconContainer}>🏥</div>
                    </div>

                    <div className={styles.hospitalInfo}>
                      <h3 className={styles.hospitalName}>{center.name}</h3>
                      <p className={styles.hospitalType}>{center.type}</p>
                      <p className={styles.hospitalLocation}>
                        {center.location}
                      </p>

                      <div className={styles.ratingSection}>
                        <div className={styles.rating}>
                          <span className={styles.ratingBadge}>
                            <span className={styles.thumbsUp}>👍</span>
                            {center.rating}
                          </span>
                        </div>
                        <span className={styles.reviewCount}>
                          ({center.reviews} Reviews)
                        </span>
                      </div>

                      <div className={styles.priceSection}>
                        <span className={styles.price}>{center.price}</span>
                        <span className={styles.originalPrice}>
                          {center.originalPrice}
                        </span>
                        <span className={styles.consultation}>
                          Consultation fee at clinic
                        </span>
                      </div>

                      <div className={styles.availability}>
                        <span className={styles.availabilityIcon}>✓</span>
                        {center.availability}
                      </div>
                    </div>

                    <div className={styles.bookingSection}>
                      <div className={styles.timeSlots}>
                        {center.timeSlots.slice(0, 3).map((slot, index) => (
                          <button key={index} className={styles.timeSlot}>
                            {slot}
                          </button>
                        ))}
                      </div>
                      <button className={styles.viewAllSlots}>View All</button>
                      <button className={styles.bookButton}>
                        Book FREE Center Visit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className={styles.sidebar}>
              <div className={styles.adBanner}>
                <div className={styles.placeholderAd}>
                  <img
                    src={sensodyne_dweb}
                    alt="Sensodyne is best"
                    className={styles.adImage}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default FindDoctors;
