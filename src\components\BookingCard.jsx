import { dateUtils } from '../utils/dateUtils';
import styles from '../styles/BookingCard.module.css';

const BookingCard = ({ booking, onCancel }) => {
  const appointmentDate = new Date(booking.appointmentDate);
  const isUpcoming = appointmentDate >= new Date().setHours(0, 0, 0, 0);
  const isPast = !isUpcoming;

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = () => {
    if (isPast) return styles.past;
    return styles.upcoming;
  };

  return (
    <div className={`${styles.bookingCard} ${getStatusColor()}`}>
      <div className={styles.cardHeader}>
        <div className={styles.hospitalInfo}>
          <h3 className={styles.hospitalName}>{booking.hospitalName}</h3>
          <p className={styles.address}>
            {[booking.address, booking.city, booking.state, booking.zipCode]
              .filter(Boolean)
              .join(', ')}
          </p>
        </div>
        <div className={styles.status}>
          <span className={`${styles.statusBadge} ${getStatusColor()}`}>
            {isPast ? 'Completed' : 'Upcoming'}
          </span>
        </div>
      </div>

      <div className={styles.appointmentDetails}>
        <div className={styles.dateTimeSection}>
          <div className={styles.dateInfo}>
            <span className={styles.label}>Date</span>
            <span className={styles.value}>{formatDate(booking.appointmentDate)}</span>
          </div>
          <div className={styles.timeInfo}>
            <span className={styles.label}>Time</span>
            <span className={styles.value}>{booking.appointmentTime}</span>
          </div>
          <div className={styles.periodInfo}>
            <span className={styles.label}>Period</span>
            <span className={styles.value}>{booking.timePeriod}</span>
          </div>
        </div>

        <div className={styles.bookingMeta}>
          <div className={styles.bookingDate}>
            <span className={styles.label}>Booked on</span>
            <span className={styles.value}>
              {new Date(booking.bookingDate).toLocaleDateString('en-US')}
            </span>
          </div>
        </div>
      </div>

      <div className={styles.cardActions}>
        {isUpcoming && (
          <button 
            className={styles.cancelButton}
            onClick={onCancel}
          >
            Cancel Appointment
          </button>
        )}
        <div className={styles.appointmentType}>
          <span className={styles.freeConsultation}>Free Consultation</span>
        </div>
      </div>
    </div>
  );
};

export default BookingCard;
