.myBookings {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.header {
  text-align: center;
  margin-bottom: 48px;
}

.header h1 {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #666;
}

.bookingsGrid {
  display: grid;
  gap: 24px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2aa7ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.noBookings {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.noBookingsIcon {
  font-size: 64px;
  margin-bottom: 24px;
}

.noBookings h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
}

.noBookings p {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.searchButton {
  background-color: #2aa7ff;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.3s ease;
}

.searchButton:hover {
  background-color: #1e90ff;
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 20px 16px;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .header {
    margin-bottom: 32px;
  }
  
  .noBookings {
    padding: 40px 20px;
  }
  
  .noBookingsIcon {
    font-size: 48px;
  }
  
  .noBookings h2 {
    font-size: 20px;
  }
}
