.myBookings {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.headerSection {
  background: linear-gradient(81deg, #E7F0FF 9.01%, rgba(232, 241, 255, 0.47) 89.11%);
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.contentWrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
  margin-top: 32px;
}

.bookingsSection {
  flex: 1;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.adBanner {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.adImage {
  width: 100%;
  height: auto;
  display: block;
}

.placeholderAd {
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
}

.placeholderAd h3 {
  margin-bottom: 8px;
  font-size: 18px;
}

.placeholderAd p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.headerContent h1 {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.searchContainer {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 400px;
}

.searchInput {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
}

.searchInput::placeholder {
  color: #999;
}

.searchButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.searchButton:hover {
  background-color: #1e90ff;
}

.promoBanner {
  background: linear-gradient(135deg, #2aa7ff 0%, #1e90ff 100%);
  border-radius: 16px;
  margin-bottom: 40px;
  overflow: hidden;
}

.promoContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px;
}

.promoText {
  color: white;
  flex: 1;
}

.promoText h3 {
  font-size: 16px;
  font-weight: 400;
  margin: 0 0 8px 0;
  opacity: 0.9;
}

.promoText h2 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.highlight {
  color: #FFD700;
}

.promoButton {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.promoButton:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.promoImage {
  flex-shrink: 0;
  margin-left: 32px;
}

.promoImage img {
  width: 200px;
  height: auto;
  border-radius: 8px;
}

.bookingsGrid {
  display: grid;
  gap: 24px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2aa7ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.noBookings {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.noBookingsIcon {
  font-size: 64px;
  margin-bottom: 24px;
}

.noBookings h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
}

.noBookings p {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.searchButton {
  background-color: #2aa7ff;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.3s ease;
}

.searchButton:hover {
  background-color: #1e90ff;
  transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 20px 16px;
  }

  .contentWrapper {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .sidebar {
    order: -1;
  }

  .headerContent {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .headerContent h1 {
    font-size: 24px;
  }

  .searchContainer {
    min-width: auto;
    width: 100%;
    max-width: 400px;
  }

  .promoContent {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }

  .promoImage {
    margin-left: 0;
    margin-top: 20px;
  }

  .promoText h2 {
    font-size: 24px;
  }

  .noBookings {
    padding: 40px 20px;
  }

  .noBookingsIcon {
    font-size: 48px;
  }

  .noBookings h2 {
    font-size: 20px;
  }
}
