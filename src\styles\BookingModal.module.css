.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;
}

.modalTitle {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 32px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: #333;
}

.modalContent {
  padding: 0 24px 24px 24px;
}

.centerInfo {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.centerInfo h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.centerInfo p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.bookingSection {
  margin-bottom: 24px;
}

.dateSelection {
  margin-bottom: 32px;
}

.dateSelection h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.dateGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.dateButton {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.dateButton:hover {
  border-color: #2aa7ff;
}

.dateButton.selected {
  background-color: #2aa7ff;
  border-color: #2aa7ff;
  color: white;
}

.dayName {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.dayNumber {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 2px;
}

.monthName {
  font-size: 12px;
  opacity: 0.8;
}

.timeSelection h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.timePeriods {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.periodButton {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.periodButton:hover {
  border-color: #2aa7ff;
}

.periodButton.activePeriod {
  background-color: #2aa7ff;
  border-color: #2aa7ff;
  color: white;
}

.timeSlots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.timeButton {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.timeButton:hover {
  border-color: #2aa7ff;
}

.timeButton.selected {
  background-color: #2aa7ff;
  border-color: #2aa7ff;
  color: white;
}

.modalActions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.cancelButton {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background-color: #f8f9fa;
}

.bookButton {
  background-color: #2aa7ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bookButton:hover:not(:disabled) {
  background-color: #1e90ff;
}

.bookButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.successMessage {
  text-align: center;
  padding: 40px 24px;
}

.successIcon {
  font-size: 64px;
  color: #28a745;
  margin-bottom: 16px;
}

.successMessage h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 8px;
}

.successMessage p {
  font-size: 16px;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 16px;
  }

  .modal {
    max-height: 95vh;
  }

  .modalHeader {
    padding: 16px 16px 0 16px;
  }

  .modalContent {
    padding: 0 16px 16px 16px;
  }

  .modalTitle {
    font-size: 20px;
  }

  .dateGrid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 8px;
  }

  .timePeriods {
    flex-direction: column;
    gap: 8px;
  }

  .timeSlots {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
  }

  .modalActions {
    flex-direction: column;
    gap: 12px;
  }

  .cancelButton,
  .bookButton {
    width: 100%;
  }
}
