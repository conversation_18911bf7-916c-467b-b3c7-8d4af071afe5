import { useState, useEffect } from "react";
import Navigation from "../components/Navigation";
import BookingCard from "../components/BookingCard";
import Footer from "../components/Footer";
import { localStorageService } from "../utils/localStorage";
import styles from "../styles/MyBookings.module.css";

const MyBookings = () => {
  const [bookings, setBookings] = useState([]);
  const [filteredBookings, setFilteredBookings] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadBookings = () => {
      try {
        const savedBookings = localStorageService.getBookings();
        // Sort bookings by appointment date (newest first)
        const sortedBookings = savedBookings.sort(
          (a, b) => new Date(a.appointmentDate) - new Date(b.appointmentDate)
        );
        setBookings(sortedBookings);
        setFilteredBookings(sortedBookings);
      } catch (error) {
        console.error("Error loading bookings:", error);
      } finally {
        setLoading(false);
      }
    };

    loadBookings();
  }, []);

  // Filter bookings based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredBookings(bookings);
    } else {
      const filtered = bookings.filter((booking) =>
        booking.hospitalName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredBookings(filtered);
    }
  }, [searchTerm, bookings]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCancelBooking = (bookingId) => {
    try {
      localStorageService.removeBooking(bookingId);
      setBookings((prevBookings) =>
        prevBookings.filter((booking) => booking.id !== bookingId)
      );
    } catch (error) {
      console.error("Error canceling booking:", error);
      alert("Failed to cancel booking. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className={styles.myBookings}>
        <Navigation />
        <div className={styles.container}>
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading your bookings...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.myBookings}>
      <Navigation />

      <div className={styles.headerSection}>
        <div className={styles.container}>
          <div className={styles.headerContent}>
            <h1>My Bookings</h1>
            <div className={styles.searchContainer}>
              <input
                type="text"
                placeholder="Search by hospital"
                className={styles.searchInput}
                value={searchTerm}
                onChange={handleSearchChange}
              />
              <button className={styles.searchButton}>🔍 Search</button>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.container}>
        <div className={styles.contentWrapper}>
          <div className={styles.bookingsSection}>
            {filteredBookings.length === 0 ? (
              <div className={styles.noBookings}>
                <div className={styles.noBookingsIcon}>📅</div>
                <h2>No bookings found</h2>
                <p>
                  {searchTerm
                    ? `No bookings found for "${searchTerm}"`
                    : "You haven't made any appointments yet."}
                </p>
                <a href="/" className={styles.searchButton}>
                  Find Medical Centers
                </a>
              </div>
            ) : (
              <div className={styles.bookingsGrid}>
                {filteredBookings.map((booking) => (
                  <BookingCard
                    key={booking.id}
                    booking={booking}
                    onCancel={() => handleCancelBooking(booking.id)}
                  />
                ))}
              </div>
            )}
          </div>

          <div className={styles.sidebar}>
            <div className={styles.adBanner}>
              <div className={styles.placeholderAd}>
                <h3>Sensodyne Advertisement</h3>
                <p>Placeholder for sensodyne_dweb.png</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default MyBookings;
