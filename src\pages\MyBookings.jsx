import { useState, useEffect } from "react";
import Navigation from "../components/Navigation";
import BookingCard from "../components/BookingCard";
import { localStorageService } from "../utils/localStorage";
import styles from "../styles/MyBookings.module.css";

const MyBookings = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadBookings = () => {
      try {
        const savedBookings = localStorageService.getBookings();
        // Sort bookings by appointment date (newest first)
        const sortedBookings = savedBookings.sort(
          (a, b) => new Date(a.appointmentDate) - new Date(b.appointmentDate)
        );
        setBookings(sortedBookings);
      } catch (error) {
        console.error("Error loading bookings:", error);
      } finally {
        setLoading(false);
      }
    };

    loadBookings();
  }, []);

  const handleCancelBooking = (bookingId) => {
    try {
      localStorageService.removeBooking(bookingId);
      setBookings((prevBookings) =>
        prevBookings.filter((booking) => booking.id !== bookingId)
      );
    } catch (error) {
      console.error("Error canceling booking:", error);
      alert("Failed to cancel booking. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className={styles.myBookings}>
        <Navigation />
        <div className={styles.container}>
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading your bookings...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.myBookings}>
      <Navigation />

      <div className={styles.headerSection}>
        <div className={styles.container}>
          <div className={styles.headerContent}>
            <h1>My Bookings</h1>
            <div className={styles.searchContainer}>
              <input
                type="text"
                placeholder="Search My Bookings"
                className={styles.searchInput}
              />
              <button className={styles.searchButton}>🔍 Search</button>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.container}>
        {bookings.length === 0 ? (
          <div className={styles.noBookings}>
            <div className={styles.noBookingsIcon}>📅</div>
            <h2>No bookings found</h2>
            <p>You haven't made any appointments yet.</p>
            <a href="/" className={styles.searchButton}>
              Find Medical Centers
            </a>
          </div>
        ) : (
          <div className={styles.bookingsGrid}>
            {bookings.map((booking) => (
              <BookingCard
                key={booking.id}
                booking={booking}
                onCancel={() => handleCancelBooking(booking.id)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyBookings;
